<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use App\Yantrana\Components\Auth\Models\AuthModel;

class SSOTokenAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // First, check if user is already authenticated
        if (Auth::check()) {
            Log::info('SSO Token Auth: User already authenticated', [
                'user_id' => Auth::id(),
                'email' => Auth::user()->email,
                'url' => $request->fullUrl()
            ]);
            return $next($request);
        }

        try {
            // Get the SSO token from query parameter
            $token = $request->query('sso_token');
            
            if (!$token) {
                Log::warning('SSO Token Auth: No token provided and user not authenticated', [
                    'url' => $request->fullUrl(),
                    'method' => $request->method()
                ]);
                
                // For web routes, show an error page instead of JSON
                if ($request->expectsJson()) {
                    return response()->json([
                        'error' => 'Authentication required',
                        'message' => 'Please provide a valid SSO token or log in to access this page.'
                    ], 401);
                } else {
                    return response()->view('errors.sso-token-error', [
                        'error' => 'Authentication Required',
                        'message' => 'Please provide a valid SSO token to access this page.',
                        'details' => 'No SSO token was provided in the URL and you are not currently logged in.'
                    ], 401);
                }
            }

            // Decode JWT token
            $payload = JWT::decode($token, new Key(env('SSO_SECRET', 'your-secret-key'), 'HS256'));
            
            if (!isset($payload->email)) {
                Log::error('SSO Token Auth: Invalid token payload', [
                    'url' => $request->fullUrl(),
                    'payload' => $payload
                ]);
                
                if ($request->expectsJson()) {
                    return response()->json([
                        'error' => 'Invalid token payload',
                        'message' => 'Token does not contain required user information'
                    ], 401);
                } else {
                    return response()->view('errors.sso-token-error', [
                        'error' => 'Invalid Token Payload',
                        'message' => 'The SSO token does not contain required user information.',
                        'details' => 'Token payload is missing email field.'
                    ], 401);
                }
            }

            // Find user by email using AuthModel
            $user = AuthModel::where('email', $payload->email)->first();
            
            if (!$user) {
                Log::error('SSO Token Auth: User not found', [
                    'email' => $payload->email,
                    'url' => $request->fullUrl()
                ]);
                
                if ($request->expectsJson()) {
                    return response()->json([
                        'error' => 'User not found',
                        'message' => 'User with this email does not exist'
                    ], 404);
                } else {
                    return response()->view('errors.sso-token-error', [
                        'error' => 'User Not Found',
                        'message' => 'The user associated with this SSO token does not exist.',
                        'details' => 'User with email "' . $payload->email . '" was not found in the system.'
                    ], 404);
                }
            }

            // Check user status
            if ($user->status != 1) {
                Log::error('SSO Token Auth: User account is not active', [
                    'email' => $user->email,
                    'status' => $user->status,
                    'url' => $request->fullUrl()
                ]);
                
                if ($request->expectsJson()) {
                    return response()->json([
                        'error' => 'User account is not active',
                        'message' => 'Your account is not in active mode, please contact administrator.'
                    ], 403);
                } else {
                    return response()->view('errors.sso-token-error', [
                        'error' => 'Account Not Active',
                        'message' => 'Your account is not in active mode.',
                        'details' => 'Please contact your administrator to activate your account.'
                    ], 403);
                }
            }

            // Authenticate the user for this request
            Auth::loginUsingId($user->_id);
            
            // Regenerate session for security (same as SSOLoginController)
            $request->session()->regenerate();
            
            Log::info('SSO Token Auth: User authenticated successfully', [
                'user_id' => $user->_id,
                'email' => $user->email,
                'url' => $request->fullUrl()
            ]);

            return $next($request);

        } catch (\Firebase\JWT\ExpiredException $e) {
            Log::error('SSO Token Auth: Token has expired', [
                'url' => $request->fullUrl(),
                'error' => $e->getMessage()
            ]);
            
            if ($request->expectsJson()) {
                return response()->json([
                    'error' => 'Token has expired',
                    'message' => 'Your SSO token has expired, please request a new one'
                ], 401);
            } else {
                return response()->view('errors.sso-token-error', [
                    'error' => 'Token Expired',
                    'message' => 'Your SSO token has expired.',
                    'details' => 'Please request a new token from your administrator.'
                ], 401);
            }
        } catch (\Firebase\JWT\InvalidTokenException $e) {
            Log::error('SSO Token Auth: Invalid token', [
                'url' => $request->fullUrl(),
                'error' => $e->getMessage()
            ]);
            
            if ($request->expectsJson()) {
                return response()->json([
                    'error' => 'Invalid token',
                    'message' => 'The provided SSO token is invalid'
                ], 401);
            } else {
                return response()->view('errors.sso-token-error', [
                    'error' => 'Invalid Token',
                    'message' => 'The provided SSO token is invalid.',
                    'details' => 'Please check your token and try again.'
                ], 401);
            }
        } catch (\Exception $e) {
            Log::error('SSO Token Auth Error: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'request_url' => $request->fullUrl(),
                'request_method' => $request->method()
            ]);
            
            if ($request->expectsJson()) {
                return response()->json([
                    'error' => 'Authentication failed',
                    'message' => 'An error occurred during authentication'
                ], 500);
            } else {
                return response()->view('errors.sso-token-error', [
                    'error' => 'Authentication Failed',
                    'message' => 'An error occurred during authentication.',
                    'details' => 'Please try again or contact your administrator.'
                ], 500);
            }
        }
    }
} 