

<?php $__env->startSection('content'); ?>
<div class="lw-page-content">
    <!-- Page header -->
    <div class="lw-page-header">
        <div class="container-fluid mt-7">
            <div class="d-flex flex-wrap justify-content-between align-items-center border-bottom pb-3 mb-4">
                <!-- Left: Title + Breadcrumb -->
                <div class="d-flex flex-column flex-md-row align-items-start align-items-md-center gap-2">
                    <h1 class="mb-0 text-success font-weight-bold" style="color: #146c43;">
                        <i class="fas fa-shopping-cart mr-2"></i> <?php echo e(__tr('WhatsApp Orders')); ?>

                    </h1>
                    <ol class="breadcrumb mb-0 ml-md-3">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('vendor.console')); ?>"><?php echo e(__tr('Home')); ?></a></li>
                        <li class="breadcrumb-item active"><?php echo e(__tr('WhatsApp Orders')); ?></li>
                    </ol>
                </div>

                <!-- Right: Action Buttons -->
                <div class="d-flex gap-2 mt-7 mt-md-0">
                    <button type="button" class="btn btn-dark-green" style="margin-right: 8px;" onclick="refreshOrders()">
                        <i class="fas fa-sync-alt mr-1"></i> <?php echo e(__tr('Refresh')); ?>

                    </button>
                    <!-- <button type="button" class="btn btn-outline-dark-green" onclick="exportOrders()">
                        <i class="fa fa-download mr-1"></i> <?php echo e(__tr('Export')); ?>

                    </button> -->
                </div>
            </div>
        </div>

    </div>

    <!-- Page content -->
    <div class="container-fluid">
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card border-0 shadow-sm rounded p-4 animated-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <!-- Text -->
                        <div>
                            <h3 class="mb-1 text-dark" style="font-weight: 600;"><?php echo e(__tr('Total Orders')); ?></h3>
                            <h2 class="mb-1 text-dark" style="font-weight: 800;"><?php echo e(number_format($orderStatistics['total_orders'] ?? 0)); ?></h2>
                            <!-- <span class="text-success fw-medium" style="font-size: 14px;">+12.5% from last month
                            </span> -->
                        </div>

                        <!-- Icon -->
                        <div class="rounded d-flex align-items-center justify-content-center" style="background-color: #DCFCE7; padding: 14px;">
                            <i class="fa fa-shopping-cart" style="color: #22A755; font-size: 24px;"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card border-0 shadow-sm rounded p-4 animated-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <!-- Text Section -->
                        <div>
                            <h3 class="mb-1 text-dark" style="font-weight: 600;"><?php echo e(__tr('Pending Orders')); ?></h3>
                            <h2 class="mb-1 text-dark" style="font-weight: 800;"><?php echo e($orderStatistics['pending_orders'] ?? 0); ?></h2>
                            <!-- <span class="text-success fw-medium" style="font-size: 14px;">+3.2% from last month
                            </span> -->
                        </div>
                        <!-- Icon Section -->
                        <div class="rounded d-flex align-items-center justify-content-center" style="background-color: #fff8cc; padding: 14px;">
                            <i class="fas fa-clock" style="color: #e6b200; font-size: 24px;"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card border-0 shadow-sm rounded p-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <!-- Text Section -->
                        <div>
                            <h3 class="mb-1" style="color: #555; font-weight: 600;"><?php echo e(__tr('Completed Orders')); ?></h3>
                            <h2 class="mb-1" style="color: #111; font-weight: bold;">
                                <?php echo e(number_format($orderStatistics['completed_orders'] ?? 0)); ?>

                            </h2>
                            <!-- <span class="text-success" style="font-size: 14px;">+8.1% from last month
                            </span> -->
                        </div>

                        <!-- Icon Section -->
                        <div class="rounded d-flex align-items-center justify-content-center"
                            style="background-color: #e6f8ee; padding: 16px;">
                            <i class="fa fa-check-circle" style="color: #22A755; font-size: 24px;"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card border-0 shadow-sm rounded p-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <!-- Text Section -->
                        <div>
                            <h3 class="mb-1" style="color: #555; font-weight: 600;"><?php echo e(__tr('Total Revenue')); ?></h3>
                            <h2 class="mb-1" style="color: #111; font-weight: bold;">
                                ₹<?php echo e(number_format($orderStatistics['total_revenue'] ?? 0)); ?>

                            </h2>
                            <!-- <span class="text-success" style="font-size: 14px;">+15.3% from last month
                            </span> -->
                        </div>

                        <!-- Icon Section -->
                        <div class="rounded d-flex align-items-center justify-content-center"
                            style="background-color: #e6f0fd; padding: 16px;">
                            <i class="fas fa-rupee-sign" style="color: #2b6cb0; font-size: 24px;"></i>
                        </div>
                    </div>
                </div>
            </div>

        </div>

        <!-- Revenue Statistics -->
        <div class="row mb-4">
            <div class="col-lg-6 col-md-6 mb-4">
                <div class="card border-0 shadow-sm rounded p-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <!-- Text Section -->
                        <div>
                            <h3 class="mb-1" style="color: #555; font-weight: 600;"><?php echo e(__tr('Total Revenue')); ?></h3>
                            <h2 class="mb-1" style="color: #111; font-weight: bold;">
                                ₹<?php echo e(formatAmount($orderStatistics['total_revenue'] ?? 0)); ?>

                            </h2>
                            <!-- <span class="text-success" style="font-size: 14px;">+15.3% from last month
                            </span> -->
                        </div>

                        <!-- Icon Section -->
                        <div class="rounded d-flex align-items-center justify-content-center"
                            style="background-color: #e6f0fd; padding: 16px;">
                            <i class="fas fa-rupee-sign" style="color: #2b6cb0; font-size: 24px;"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-6 col-md-6 mb-4">
                <div class="card border-0 shadow-sm rounded p-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <!-- Text Section -->
                        <div>
                            <h3 class="mb-1" style="color: #555; font-weight: 600;"><?php echo e(__tr('Pending Revenue')); ?></h3>
                            <h2 class="mb-1" style="color: #111; font-weight: bold;">
                                <?php echo e(formatAmount($orderStatistics['pending_revenue'] ?? 0)); ?>

                            </h2>
                            <!-- <span class="text-success" style="font-size: 14px;">+6.7% from last month
                            </span> -->
                        </div>

                        <!-- Icon Section -->
                        <div class="rounded d-flex align-items-center justify-content-center"
                            style="background-color: #fef8e7; padding: 16px;">
                            <i class="fa fa-check-circle" style="color: #f1b600; font-size: 24px;"></i>
                        </div>
                    </div>
                </div>
            </div>

        </div>

        <!-- Filters -->
        <div class="card mb-4">
            <div class="card-body">
                <form id="ordersFilterForm">
                    <div class="row g-3 align-items-end">
                        <!-- Status -->
                        <div class="col-md-3">
                            <label class="mb-1 text-dark" style="font-weight: 600;"><?php echo e(__tr('Status')); ?></label>
                            <select name="status" class="form-control">
                                <option value=""><?php echo e(__tr('All Statuses')); ?></option>
                                <option value="pending"><?php echo e(__tr('Pending')); ?></option>
                                <option value="awaiting_address"><?php echo e(__tr('Awaiting Address')); ?></option>
                                <option value="awaiting_payment"><?php echo e(__tr('Awaiting Payment')); ?></option>
                                <option value="paid"><?php echo e(__tr('Paid')); ?></option>
                                <option value="confirmed"><?php echo e(__tr('Confirmed')); ?></option>
                                <option value="shipped"><?php echo e(__tr('Shipped')); ?></option>
                                <option value="delivered"><?php echo e(__tr('Delivered')); ?></option>
                                <option value="cancelled"><?php echo e(__tr('Cancelled')); ?></option>
                            </select>
                        </div>

                        <!-- Order ID -->
                        <div class="col-md-3">
                            <label class="mb-1 text-dark" style="font-weight: 600;"><?php echo e(__tr('Order ID')); ?></label>
                            <div class="input-group">
                                <span class="input-group-text bg-white">
                                    <i class="fa fa-search text-muted"></i>
                                </span>
                                <input 
                                    type="text" 
                                    name="order_id" 
                                    class="form-control border-start-0" 
                                    placeholder="<?php echo e(__tr('Search by Order ID')); ?>"
                                    oninput="this.style.boxShadow = this.value ? '0 0 0 0.2rem rgba(20, 83, 45, 0.6)' : 'none'"
                                >
                            </div>
                        </div>


                        <!-- Customer Phone -->
                        <div class="col-md-3">
                            <label class="mb-1 text-dark" style="font-weight: 600;"><?php echo e(__tr('Customer Phone')); ?></label>
                            <input 
                                type="text" 
                                name="customer_phone" 
                                class="form-control" 
                                placeholder="<?php echo e(__tr('Enter phone number')); ?>"
                                oninput="this.style.boxShadow = this.value ? '0 0 0 0.2rem rgba(20, 83, 45, 0.6)' : 'none'"
                            >
                        </div>

                        <!-- Buttons -->
                        <div class="col-md-3 text-end">
                            <div class="d-flex justify-content-end gap-2">
                                <button type="button" 
                                    onclick="clearFilters()" 
                                    style="
                                        margin-right: 8px;
                                        border: 1px solid #6c757d;
                                        color: #6c757d;
                                        border-radius: 8px;
                                        padding: 6px 16px;
                                        background-color: transparent;
                                        transition: all 0.3s ease;
                                        position: relative;
                                        overflow: hidden;
                                    " 
                                    onmouseover="this.style.transform='scale(1.05)'; this.style.boxShadow='0 4px 12px rgba(0,0,0,0.1)'; this.style.backgroundColor='#f8f9fa'; this.style.color='#212529'; this.style.borderColor='#5c636a'"
                                    onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='none'; this.style.backgroundColor='transparent'; this.style.color='#6c757d'; this.style.borderColor='#6c757d'"
                                    onmousedown="this.style.setProperty('--ripple', 'active')"
                                >
                                    <i class="far fa-times-circle text-danger"></i> <?php echo e(__tr('Clear')); ?>

                                </button>
                               <button type="submit" 
                                    class="btn" 
                                    style="
                                        background-color: #15803D;
                                        color: #fff;
                                        border-radius: 8px;
                                        padding: 10px 16px;
                                        border: 1px solid #14532d;
                                        transition: all 0.3s ease;
                                        transform: scale(1);
                                    " 
                                    onmouseover="this.style.backgroundColor='#166534'; this.style.transform='scale(1.05)'; this.style.boxShadow='0 4px 12px rgba(0,0,0,0.15)'"
                                    onmouseout="this.style.backgroundColor='#15803D'; this.style.transform='scale(1)'; this.style.boxShadow='none'"
                                >
                                    <i class="fa fa-filter me-1"></i> <?php echo e(__tr('Apply Filters')); ?>

                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>




        <!-- Orders Table -->
        <div class="card">
            <div class="card-header">
                <h3 class="mb-0"><?php echo e(__tr('Orders List')); ?></h3>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped" id="ordersTable">
                        <thead>
                            <tr>
                                <th><?php echo e(__tr('Order ID')); ?></th>
                                <th><?php echo e(__tr('Customer')); ?></th>
                                <th><?php echo e(__tr('Items')); ?></th>
                                <th><?php echo e(__tr('Amount')); ?></th>
                                <th><?php echo e(__tr('Status')); ?></th>
                                <th><?php echo e(__tr('Date')); ?></th>
                                <th><?php echo e(__tr('Actions')); ?></th>
                            </tr>
                        </thead>
                        <tbody id="ordersTableBody">
                            <!-- Orders will be loaded here via AJAX -->
                        </tbody>
                    </table>
                </div>
                <div id="ordersPagination" class="mt-3">
                    <!-- Pagination will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Order Status Update Modal -->
<div class="modal fade" id="updateStatusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><?php echo e(__tr('Update Order Status')); ?></h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="updateStatusForm">
                <div class="modal-body">
                    <input type="hidden" id="orderUidForUpdate" name="order_uid">
                    <div class="form-group">
                        <label><?php echo e(__tr('New Status')); ?></label>
                        <select name="status" class="form-control" required>
                            <option value="pending"><?php echo e(__tr('Pending')); ?></option>
                            <option value="awaiting_address"><?php echo e(__tr('Awaiting Address')); ?></option>
                            <option value="awaiting_payment"><?php echo e(__tr('Awaiting Payment')); ?></option>
                            <option value="paid"><?php echo e(__tr('Paid')); ?></option>
                            <option value="confirmed"><?php echo e(__tr('Confirmed')); ?></option>
                            <option value="shipped"><?php echo e(__tr('Shipped')); ?></option>
                            <option value="delivered"><?php echo e(__tr('Delivered')); ?></option>
                            <option value="cancelled"><?php echo e(__tr('Cancelled')); ?></option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo e(__tr('Cancel')); ?></button>
                    <button type="submit" class="btn btn-primary"><?php echo e(__tr('Update Status')); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('appScripts'); ?>
<script>
let currentPage = 1;
let currentFilters = {};

$(document).ready(function() {
    // Setup CSRF token for all AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    
    // Load initial data
    loadOrders();
    loadOrderStatistics();
    
    // Filter form submission
    $('#ordersFilterForm').on('submit', function(e) {
        e.preventDefault();
        currentPage = 1;
        currentFilters = $(this).serialize();
        loadOrders();
    });
    
    // Status update form submission
    $('#updateStatusForm').on('submit', function(e) {
        e.preventDefault();
        updateOrderStatus();
    });
});

function loadOrders(page = 1) {
    currentPage = page;
    
    $.ajax({
        url: "<?php echo e(route('vendor.whatsapp.orders.data')); ?>",
        method: 'GET',
        data: currentFilters + '&page=' + page,
        beforeSend: function() {
            $('#ordersTableBody').html('<tr><td colspan="7" class="text-center"><?php echo e(__tr("Loading...")); ?></td></tr>');
        },
        success: function(response) {
            if (response.reaction == 1) {
                renderOrdersTable(response.data.orders);
                renderPagination(response.data.orders);
            } else {
                showErrorMessage('<?php echo e(__tr("Failed to load orders")); ?>');
            }
        },
        error: function() {
            showErrorMessage('<?php echo e(__tr("Error loading orders")); ?>');
        }
    });
}

function renderOrdersTable(orders) {
    let html = '';
    
    if (orders.data && orders.data.length > 0) {
        orders.data.forEach(function(order) {
            html += `
                <tr>
                    <td><strong>${order.order_id}</strong></td>
                    <td>
                        <div>${order.customer_name || 'N/A'}</div>
                        <small class="text-muted">${order.customer_phone}</small>
                    </td>
                    <td>${order.items ? order.items.length : 0} items</td>
                    <td><strong>${order.formatted_total_amount}</strong></td>
                    <td><span class="badge badge-${getStatusColor(order.status)}">${order.status_label}</span></td>
                    <td>${formatDate(order.created_at)}</td>
                    <td>
                        <button class="btn btn-sm btn-primary" onclick="viewOrder('${order._uid}')">
                            <i class="fa fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="showUpdateStatusModal('${order._uid}', '${order.status}')">
                            <i class="fa fa-edit"></i>
                        </button>
                    </td>
                </tr>
            `;
        });
    } else {
        html = '<tr><td colspan="7" class="text-center"><?php echo e(__tr("No orders found")); ?></td></tr>';
    }
    
    $('#ordersTableBody').html(html);
}

function getStatusColor(status) {
    const colors = {
        'pending': 'warning',
        'awaiting_address': 'info',
        'awaiting_payment': 'warning',
        'paid': 'success',
        'confirmed': 'success',
        'shipped': 'primary',
        'delivered': 'success',
        'cancelled': 'danger',
        'refunded': 'secondary'
    };
    return colors[status] || 'secondary';
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString() + ' ' + new Date(dateString).toLocaleTimeString();
}

function viewOrder(orderUid) {
    window.location.href = "<?php echo e(route('vendor.whatsapp.orders.details', ':orderUid')); ?>".replace(':orderUid', orderUid);
}

function showUpdateStatusModal(orderUid, currentStatus) {
    $('#orderUidForUpdate').val(orderUid);
    $('#updateStatusForm select[name="status"]').val(currentStatus);
    $('#updateStatusModal').modal('show');
}

function updateOrderStatus() {
    const orderUid = $('#orderUidForUpdate').val();
    const formData = $('#updateStatusForm').serialize();
    
    $.ajax({
        url: "<?php echo e(route('vendor.whatsapp.orders.update_status', ':orderUid')); ?>".replace(':orderUid', orderUid),
        method: 'PATCH',
        data: formData,
        beforeSend: function() {
            // Disable submit button to prevent double submission
            $('#updateStatusForm button[type="submit"]').prop('disabled', true);
        },
        success: function(response) {
            if (response.reaction == 1) {
                $('#updateStatusModal').modal('hide');
                showSuccessMessage('<?php echo e(__tr("Order status updated successfully")); ?>');
                // Refresh both orders list and statistics
                loadOrders(currentPage);
                loadOrderStatistics();
            } else if (response.reaction == 2 && response.data.message === 'Token Expired, Please reload and try again.') {
                showErrorMessage('<?php echo e(__tr("Session expired. The page will refresh.")); ?>');
                setTimeout(function() {
                    window.location.reload();
                }, 2000);
            } else {
                showErrorMessage(response.data.message || '<?php echo e(__tr("Failed to update order status")); ?>');
            }
        },
        error: function() {
            showErrorMessage('<?php echo e(__tr("Error updating order status")); ?>');
        },
        complete: function() {
            // Re-enable submit button
            $('#updateStatusForm button[type="submit"]').prop('disabled', false);
        }
    });
}

function refreshOrders() {
    loadOrders(currentPage);
    loadOrderStatistics();
}

function loadOrderStatistics() {
    $.ajax({
        url: "<?php echo e(route('vendor.whatsapp.orders.statistics')); ?>",
        method: 'GET',
        success: function(response) {
            if (response.reaction == 1) {
                updateStatisticsCards(response.data.order_statistics);
            }
        },
        error: function() {
            console.log('Error loading order statistics');
        }
    });
}

function updateStatisticsCards(stats) {
    // Update the statistics cards with new data
    $('.card .mb-0:contains("<?php echo e(__tr('Total Orders')); ?>")').prev().text(stats.total_orders || 0);
    $('.card .mb-0:contains("<?php echo e(__tr('Pending Orders')); ?>")').prev().text(stats.pending_orders || 0);
    $('.card .mb-0:contains("<?php echo e(__tr('Completed Orders')); ?>")').prev().text(stats.completed_orders || 0);
    $('.card .mb-0:contains("<?php echo e(__tr('Cancelled Orders')); ?>")').prev().text(stats.cancelled_orders || 0);
    
    // Format and update revenue amounts
    const totalRevenue = formatCurrency(stats.total_revenue || 0);
    const pendingRevenue = formatCurrency(stats.pending_revenue || 0);
    
    $('.card .mb-0:contains("<?php echo e(__tr('Total Revenue')); ?>")').prev().text(totalRevenue);
    $('.card .mb-0:contains("<?php echo e(__tr('Pending Revenue')); ?>")').prev().text(pendingRevenue);
}

function formatCurrency(amount) {
    // Simple currency formatting - you may want to use the actual currency from settings
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
}

function clearFilters() {
    $('#ordersFilterForm')[0].reset();
    currentFilters = {};
    currentPage = 1;
    loadOrders();
}

function exportOrders() {
    const params = new URLSearchParams(currentFilters);
    window.location.href = "<?php echo e(route('vendor.whatsapp.orders.export')); ?>?" + params.toString();
}

function renderPagination(orders) {
    // Simple pagination implementation
    let html = '';
    if (orders.last_page > 1) {
        html += '<nav><ul class="pagination">';
        
        // Previous button
        if (orders.current_page > 1) {
            html += `<li class="page-item"><a class="page-link" href="#" onclick="loadOrders(${orders.current_page - 1})">Previous</a></li>`;
        }
        
        // Page numbers
        for (let i = 1; i <= orders.last_page; i++) {
            if (i == orders.current_page) {
                html += `<li class="page-item active"><span class="page-link">${i}</span></li>`;
            } else {
                html += `<li class="page-item"><a class="page-link" href="#" onclick="loadOrders(${i})">${i}</a></li>`;
            }
        }
        
        // Next button
        if (orders.current_page < orders.last_page) {
            html += `<li class="page-item"><a class="page-link" href="#" onclick="loadOrders(${orders.current_page + 1})">Next</a></li>`;
        }
        
        html += '</ul></nav>';
    }
    
    $('#ordersPagination').html(html);
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', ['title' => __tr('WhatsApp Orders')], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\omx-flow-new\resources\views/whatsapp-service/orders-list.blade.php ENDPATH**/ ?>